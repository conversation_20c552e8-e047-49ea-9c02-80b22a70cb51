# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure(2) do |config|

  # Replace this with "fedora/34-cloud-base" (or latest) for a fresh box without
  # pre-installed freeipa packages, you must also uncomment shell provisioning step
  # at the bottom of this file.
  # config.vm.box = "fedora/34-cloud-base"
  config.vm.box = "freeipa/freeipa-workshop"

  config.vm.synced_folder ".", "/vagrant", disabled: true

  config.vm.provider :libvirt do |libvirt|
    libvirt.qemu_use_session = false
    libvirt.memory = 2048
  end

  config.vm.provider :virtualbox do |virtualbox|
    virtualbox.memory = 2048
  end

  config.vm.define "server" do |server|
    server.vm.network "private_network", ip: "*************"
    server.vm.hostname = "server.ipademo.local"

  end

  config.vm.define "replica" do |replica|
    replica.vm.network "private_network", ip: "*************"
    replica.vm.hostname = "replica.ipademo.local"

    replica.vm.provision "shell",
      inline: 'echo "PEERDNS=no" >> /etc/sysconfig/network-scripts/ifcfg-eth0'
    replica.vm.provision "shell",
      inline: 'echo "DNS1=*************" >> /etc/sysconfig/network-scripts/ifcfg-eth1'
    replica.vm.provision "shell",
      inline: 'printf "DNS=*************\nDomains=~." >> /etc/systemd/resolved.conf'
    replica.vm.provision "shell",
      inline: 'systemctl restart systemd-resolved'
  end

  config.vm.define "client" do |client|
    client.vm.network "private_network", ip: "*************"
    client.vm.hostname = "client.ipademo.local"

    client.vm.provision "shell",
      inline: 'echo "PEERDNS=no" >> /etc/sysconfig/network-scripts/ifcfg-eth0'
    client.vm.provision "shell",
      inline: 'echo "DNS1=*************" >> /etc/sysconfig/network-scripts/ifcfg-eth1'
    client.vm.provision "shell",
      inline: 'printf "DNS=*************\nDomains=~." >> /etc/systemd/resolved.conf'
    client.vm.provision "shell",
      inline: 'systemctl restart systemd-resolved'
    client.vm.provision "shell",
      inline: 'sudo sed -i "s/^/#/g" /etc/httpd/conf.d/ssl.conf'
    client.vm.provision "shell",
      inline: 'systemctl -q enable httpd && systemctl start httpd'
    client.vm.provision "shell",
      inline: 'systemctl -q enable oddjobd && systemctl start oddjobd'
  end

  # Uncomment line below when using Fedora's cloud base box.
  # config.vm.provision "shell", path: "workshop-install-packages.sh"
  config.vm.provision "shell", path: "workshop-ipa-customizations.sh"

end
