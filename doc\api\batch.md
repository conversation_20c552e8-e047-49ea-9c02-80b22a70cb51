[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# batch
Make multiple ipa calls via one remote procedure call

### Arguments
|Name|Type|Required
|-|-|-
|methods|:ref:`Dict<Dict>`|False

### Options
* keeponly : :ref:`Str<Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|count|Output
|results|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences