#!/bin/bash

echo "=== Uniqueness插件详细检查 ==="
echo

# 1. 检查krbCanonicalName uniqueness插件
echo "1. krbCanonicalName uniqueness插件配置："
ldapsearch -Y GSSAPI -b "cn=krbCanonicalName uniqueness,cn=plugins,cn=config" '*' 2>/dev/null || echo "插件不存在或无法访问"
echo

# 2. 检查所有uniqueness插件
echo "2. 所有uniqueness插件："
ldapsearch -Y GSSAPI -b "cn=plugins,cn=config" "(cn=*uniqueness*)" cn nsslapd-pluginEnabled 2>/dev/null
echo

# 3. 检查krbPrincipalName uniqueness插件
echo "3. krbPrincipalName uniqueness插件配置："
ldapsearch -Y GSSAPI -b "cn=krbPrincipalName uniqueness,cn=plugins,cn=config" '*' 2>/dev/null || echo "插件不存在或无法访问"
echo

echo "=== 检查完成 ==="
