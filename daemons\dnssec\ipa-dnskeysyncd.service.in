[Unit]
Description=IPA key daemon

[Service]
Environment=LC_ALL=C.UTF-8
EnvironmentFile=@sysconfenvdir@/ipa-dnskeysyncd
ExecStartPre=/bin/sh -c '/bin/sed -e "s,@DNSSEC_TOKENS_DIR@,${DNSSEC_TOKENS_DIR},g;s,@DNSSEC_SOFTHSM_PIN@,${DNSSEC_SOFTHSM_PIN},g" @IPA_DATA_DIR@/ipa-dnssec.conf | /usr/bin/systemd-tmpfiles --create -'
ExecStart=@libexecdir@/ipa/ipa-dnskeysyncd
User=@ODS_USER@
Group=@NAMED_GROUP@
SupplementaryGroups=@ODS_USER@
PrivateTmp=yes
Restart=on-failure
RestartSec=60s

[Install]
WantedBy=multi-user.target
