.\" A man page for ipa-certupdate
.\" Copyright (C) 2014 Red Hat, Inc.
.\"
.\" This program is free software; you can redistribute it and/or modify
.\" it under the terms of the GNU General Public License as published by
.\" the Free Software Foundation, either version 3 of the License, or
.\" (at your option) any later version.
.\"
.\" This program is distributed in the hope that it will be useful, but
.\" WITHOUT ANY WARRANTY; without even the implied warranty of
.\" MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
.\" General Public License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see <http://www.gnu.org/licenses/>.
.\"
.\" Author: <PERSON> <jcho<PERSON>@redhat.com>
.\"
.TH "ipa-certupdate" "1" "Jul 2 2014" "IPA" "IPA Manual Pages"
.SH "NAME"
ipa\-certupdate \- Update local IPA certificate databases with certificates from the server
.SH "SYNOPSIS"
\fBipa\-certupdate\fR [\fIOPTIONS\fR...]
.SH "DESCRIPTION"
\fBipa\-certupdate\fR can be used to update local IPA certificate databases with certificates from the server.
.SH "OPTIONS"
.TP
\fB\-v\fR, \fB\-\-verbose\fR
Print debugging information.
.TP
\fB\-q\fR, \fB\-\-quiet\fR
Output only errors.
.TP
\fB\-\-log\-file\fR=\fIFILE\fR
Log to the given file.
.SH "EXIT STATUS"
0 if the command was successful

1 if an error occurred
