[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# vault_remove_owner
Remove owners from a vault.

### Arguments
|Name|Type|Required
|-|-|-
|cn|:ref:`Str<Str>`|True

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* service : :ref:`Principal<Principal>`
* shared : :ref:`Flag<Flag>`
 * Default: False
* username : :ref:`Str<Str>`
* version : :ref:`Str<Str>`
* user : :ref:`Str<Str>`
* group : :ref:`Str<Str>`
* services : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|completed|Output
|failed|Output
|result|Entry

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences