.\" A man page for ipa-client-automount
.\" Copyright (C) 2012 Red Hat, Inc.
.\"
.\" This program is free software; you can redistribute it and/or modify
.\" it under the terms of the GNU General Public License as published by
.\" the Free Software Foundation, either version 3 of the License, or
.\" (at your option) any later version.
.\"
.\" This program is distributed in the hope that it will be useful, but
.\" WITHOUT ANY WARRANTY; without even the implied warranty of
.\" MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
.\" General Public License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see <http://www.gnu.org/licenses/>.
.\"
.\" Author: <PERSON> <<EMAIL>>
.\"
.TH "ipa-client-automount" "1" "May 25 2012" "IPA" "IPA Manual Pages"
.SH "NAME"
ipa\-client\-automount \- Configure automount and NFS for IPA
.SH "SYNOPSIS"
ipa\-client\-automount [\fIOPTION\fR]... <location>
.SH "DESCRIPTION"
Configures automount for IPA.

The automount configuration consists of two files:
.PP
.IP  o
/etc/nsswitch.conf
.IP  o
/etc/sysconfig/autofs

.TP
By default this will use DNS discovery to attempt to determine the IPA server(s) to use. If IPA servers are discovered then the automount client will be configured to use DNS discovery.
.TP
If DNS discovery fails or a specific server is desired, use the \-\-server option.
.TP
The default automount location is named default. To specify a different one use the \-\-location option.
.TP
The IPA client must already be configured in order to configure automount. The IPA client is configured as part of a server installation.
.TP
SSSD is configured to manage the automount maps.
.TP
The nss automount service is configured to use sss and files.
.TP
NFSv4 is also configured. The rpc.gssd and rpc.idmapd are started on clients to support Kerberos\-secured mounts.
.SH "OPTIONS"
\fB\-\-server\fR=\fISERVER\fR
Set the FQDN of the IPA server to connect to.
.TP
\fB\-\-domain\fR=\fIDOMAIN\fR
Primary DNS domain of the IPA deployment to be used for server discovery.
.TP
\fB\-\-location\fR=\fILOCATION\fR
Automount location.
.TP
\fB\-\-idmap\-domain\fR=\fIIDMAP_DOMAIN\fR
NFS domain for idmapd.conf. If unset, defaults to the IPA domain. If set to DNS, let idmapd or nfsidmap determine the domain from DNS (see idmapd(8) or nfsidmap(5) for details). If set to anything else, set idmapd.conf's Domain entry to that value.
.TP
\fB\-d\fR, \fB\-\-debug\fR
Print debugging information to stdout.
.TP
\fB\-U\fR, \fB\-\-unattended\fR
Unattended installation. The user will not be prompted.
.TP
\fB\-\-uninstall\fR
Restore the automount configuration files.

.SH "FILES"
.TP
Files that will be configured:

/etc/nsswitch.conf

/etc/sssd/sssd.conf

.SH "EXIT STATUS"
0 if the installation was successful

1 if an error occurred

2 if uninstalling and automount is not configured

3 if installing and automount already configured
