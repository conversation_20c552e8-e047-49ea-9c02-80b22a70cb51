<?xml version="1.0"?>
<oddjobconfig>
  <service name="org.freeipa.server">
    <allow user="root"/>
    <allow user="ipaapi"/>
    <object name="/">
      <interface name="org.freeipa.server">
        <method name="conncheck">
          <helper exec="@ODDJOBDIR@/org.freeipa.server.conncheck"
                  arguments="1"
                  prepend_user_name="no"
                  argument_passing_method="cmdline"/>
        </method>
        <method name="trust_enable_agent">
          <helper exec="@ODDJOBDIR@/org.freeipa.server.trust-enable-agent"
                  arguments="1"
                  prepend_user_name="no"
                  argument_passing_method="cmdline"/>
        </method>
        <method name="config_enable_sid">
          <helper exec="@ODDJOBDIR@/org.freeipa.server.config-enable-sid"
                  arguments="10"
                  prepend_user_name="no"
                  argument_passing_method="cmdline"/>
        </method>
      </interface>
      <interface name="org.freedesktop.DBus.Introspectable">
        <allow min_uid="0" max_uid="0"/>
      </interface>
    </object>
  </service>
</oddjobconfig>
