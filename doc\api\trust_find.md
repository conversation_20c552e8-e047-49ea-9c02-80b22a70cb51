[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# trust_find
Search for trusts.

### Arguments
|Name|Type|Required
|-|-|-
|criteria|:ref:`Str<Str>`|False

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* cn : :ref:`Str<Str>`
* ipantflatname : :ref:`Str<Str>`
* ipanttrusteddomainsid : :ref:`Str<Str>`
* ipantsidblacklistincoming : :ref:`Str<Str>`
* ipantsidblacklistoutgoing : :ref:`Str<Str>`
* timelimit : :ref:`Int<Int>`
* sizelimit : :ref:`Int<Int>`
* version : :ref:`Str<Str>`
* pkey_only : :ref:`Flag<Flag>`
 * Default: False

### Output
|Name|Type
|-|-
|count|Output
|result|ListOfEntries
|summary|Output
|truncated|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences