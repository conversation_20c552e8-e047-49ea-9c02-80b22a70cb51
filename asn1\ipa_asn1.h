#pragma once

#include "ipa_krb5.h"

/**
 * @brief Encodes a Get Keytab Request Control
 *
 * @param newkt     Whether this is a New Key request or a Current Key one
 * @param princ     The principal the keys belong to (this is required)
 * @param pwd       Optional, only for New Key reqs, the password to use to
 *                  create the new keys
 * @param etypes    Optional, only for New Key reqs, list of desired
 *                  enctypes
 * @param numtypes  Optional, Number of desired enctypes in etypes
 * @param buf       A void pointer wil lcontain pointer to an allocated
 *                  buffer with the serialized control, must be freed
 * @param len       Length of the returned buffer
 *
 * @return          True on success or False on failure
 */
bool ipaasn1_enc_getkt(bool newkt, const char *princ, const char *pwd,
                       long *etypes, int numtypes, void **buf, size_t *len);

/**
 * @brief Encodes a Get Keytab Reply Control
 *
 * @param kvno      The new key version number
 * @param keys      A set of keys to return to the caller
 * @param buf       A void pointer wil lcontain pointer to an allocated
 *                  buffer with the serialized control, must be freed
 * @param len       Length of the returned buffer
 *
 * @return          True on success or False on failure
 */
bool ipaasn1_enc_getktreply(int kvno, struct keys_container *keys,
                            void **buf, size_t *len);

/**
 * @brief Decodes a Get Keytab Requst Control
 *
 * @param buf       A pointer to the serialized buffer
 * @param len       The lenght of the buffer
 * @param newkt     Returns whether this is a New Key or Current Key request
 * @param princ     Returns the principal the keys belong to.
 * @param pwd       Optional: The password to use to create keys
 * @param etypes    Optional: The desired enctypes
 * @param numtypes  Optional: Number of desired enctypes in etypes
 *
 * @return          True on success or False on failure
 *
 * NOTE: princ, pwd, etypes and numtypes should be zeroed before being
 *       passed in input, and the caller may need to free them even in
 *       case of failure.
 */
bool ipaasn1_dec_getkt(void *buf, size_t len, bool *newkt,
                       char **princ, char **pwd,
                       long **etypes, int *numtypes);

/**
 * @brief Decodes a Get Keytab Reply Control
 *
 * @param buf       A pointer to the serialized buffer
 * @param len       The lenght of the buffer
 * @param kvno      The new key version number
 * @param keys      A set of keys generated by the server
 *
 * @return          True on success or False on failure
 *
 * NOTE: keys should be a zeroed structure and the caller may need to free
 *       it even in case of failure.
 */
bool ipaasn1_dec_getktreply(void *buf, size_t len,
                            int *kvno, struct keys_container *keys);
