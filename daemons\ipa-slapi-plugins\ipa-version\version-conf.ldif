dn: cn=IPA Version Replication,cn=plugins,cn=config
changetype: add
objectclass: top
objectclass: nsSlapdPlugin
objectclass: extensibleObject
cn: IPA Version Replication
nsslapd-pluginpath: libipa_repl_version
nsslapd-plugininitfunc: repl_version_plugin_init
nsslapd-plugintype: preoperation
nsslapd-pluginenabled: off
nsslapd-pluginid: ipa_repl_version
nsslapd-pluginversion: 1.0
nsslapd-pluginvendor: Red Hat, Inc.
nsslapd-plugindescription: IPA Replication version plugin
nsslapd-plugin-depends-on-type: database
nsslapd-plugin-depends-on-named: $REPLICATION_PLUGIN

