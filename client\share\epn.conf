# Global IPA-EPN [0] configuration file.
# For a complete explanation of each parameter, see the epn.conf(5)
# manual page.
# For best results, change no more than a single parameter at a time,
# and test if ipa-epn(1) still works as intended, using --dry-run when
# it makes sense.
#
# [0] https://github.com/freeipa/freeipa/blob/master/doc/designs/expiring-password-notification.md

[global]

# Specifies the SMTP server to use. 
smtp_server = localhost

# Specifies the SMTP port.
smtp_port = 25

# Specifies the id of the user to authenticate with the SMTP server.
# Default None (empty value).
# smtp_user =

# Specifies the password for the authorized user.
# Default None (empty value).
# smtp_password =

# Specifies the path to a single file in PEM format containing the certificate.
# https://docs.python.org/3/library/ssl.html#ssl.SSLContext.load_cert_chain
# Default None (empty value).
# smtp_client_cert =

# Specifies the path to a file containing the private key in. Otherwise the
# private key will be taken from certfile as well.
# https://docs.python.org/3/library/ssl.html#ssl.SSLContext.load_cert_chain
# Default None (empty value).
# smtp_client_key =

# Specifies the password for decrypting the private key. It will be ignored if
# the private key is not encrypted and no password is needed.
# https://docs.python.org/3/library/ssl.html#ssl.SSLContext.load_cert_chain
# Default None (empty value).
# smtp_client_key_pass =

# Specifies the number of seconds to wait for SMTP to respond.
smtp_timeout = 60

# Specifies the type of secure connection to make. Options are: none,
# starttls and ssl.
smtp_security = none

# Specifies the From e-mail address value in the e-mails sent. Bounces will
# be sent here.
smtp_admin = root@localhost

# Time to wait, in milliseconds, between each e-mail sent to try to avoid
# overloading the mail queue.
smtp_delay = 0

# Specifies the From: e-mail address value in the e-mails sent.
# The default when unset is noreply@ipadefaultemaildomain.
# This value can be found by running ipa config-show.
# mail_from =

# Specifies the From: name value in the e-mails-sent.
# The default when unset is IPA-EPN.
# mail_from_name =

# The list of days before a password expiration when ipa-epn should notify
# a user that their password will soon require a reset.
notify_ttls = 28, 14, 7, 3, 1

# Set the subject of the message
# msg_subject =

# Set the character set of the message.
msg_charset = utf8

# Set the message's MIME sub-content type.
msg_subtype = plain
