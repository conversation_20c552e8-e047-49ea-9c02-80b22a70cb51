libipaasn1.a is a small static convenience library used by other ipa
binaries and modules. At the moment it is not meant to be a public shared
library and stable interface, but may become one in future.

The only files that should be manually modified are:
* asn1c/ipa.asn1 - when new interfaces are added
* ipa_asn1.[ch] - to add wrappers around interfaces

ipa_asn1.[ch] are the public interface and they SHOULD NOT export generated
structures so that the autogenerated code can change w/o impacting any other
code except the internal library functions.

To regenerate the automatically generated files run the following command:
cd asn1c;
make regenerate

Remember to commit and add any new file to asn1c/Makefile.am
