/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#ifndef	_GKReply_H_
#define	_GKReply_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Int32.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct KrbKey;

/* GKReply */
typedef struct GKReply {
	Int32_t	 newkvno;
	struct keys {
		A_SEQUENCE_OF(struct KrbKey) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} keys;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GKReply_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GKReply;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "KrbKey.h"

#endif	/* _GKReply_H_ */
#include <asn_internal.h>
