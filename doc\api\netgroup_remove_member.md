[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# netgroup_remove_member
Remove members from a netgroup.

### Arguments
|Name|Type|Required
|-|-|-
|cn|:ref:`Str<Str>`|True

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`
* user : :ref:`Str<Str>`
* group : :ref:`Str<Str>`
* host : :ref:`Str<Str>`
* hostgroup : :ref:`Str<Str>`
* netgroup : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|completed|Output
|failed|Output
|result|Entry

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences