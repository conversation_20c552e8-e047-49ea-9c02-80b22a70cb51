#!/bin/bash

echo "=== CVE-2025-4404 状态检查 ==="
echo

# 1. 检查admin用户的krbCanonicalName
echo "1. 检查admin用户的krbCanonicalName设置："
ldapsearch -Y GSSAPI -b "uid=admin,cn=users,cn=accounts,dc=rois,dc=local" krbCanonicalName 2>/dev/null | grep "krbCanonicalName:"
echo

# 2. 检查刚创建的服务的krbCanonicalName
echo "2. 检查test服务的krbCanonicalName设置："
ldapsearch -Y GSSAPI -b "krbprincipalname=test/<EMAIL>,cn=services,cn=accounts,dc=rois,dc=local" krbCanonicalName 2>/dev/null | grep "krbCanonicalName:"
echo

# 3. 搜索所有拥有****************作为canonical name的条目
echo "3. 搜索所有拥有****************作为canonical name的条目："
ldapsearch -Y GSSAPI -b "dc=rois,dc=local" "(krbcanonicalname=<EMAIL>)" dn 2>/dev/null | grep "^dn:"
echo

# 4. 检查uniqueness插件状态
echo "4. 检查krbCanonicalName uniqueness插件状态："
ldapsearch -Y GSSAPI -b "cn=krbCanonicalName uniqueness,cn=plugins,cn=config" nsslapd-pluginEnabled 2>/dev/null | grep "nsslapd-pluginEnabled:"
echo

# 5. 检查是否存在升级插件
echo "5. 检查升级插件配置："
grep -r "add_admin_krbcanonicalname" /usr/share/ipa/updates/ 2>/dev/null || echo "升级插件配置未找到"
echo

echo "=== 检查完成 ==="
