#!/bin/bash

echo "=== FreeIPA 4.9.8 权限管理 ==="
echo

# 1. 检查wells用户是否存在
echo "1. 检查wells用户："
if ipa user-show wells >/dev/null 2>&1; then
    echo "✓ wells用户存在"
else
    echo "✗ wells用户不存在，需要先创建"
    echo "创建命令: ipa user-add wells --first=Wells --last=User"
    exit 1
fi
echo

# 2. 查看所有可用的特权(privileges)
echo "2. 查看所有特权："
ipa privilege-find | grep -E "(Privilege name|特权名称)" | head -20
echo "... (显示前20个)"
echo

# 3. 搜索与用户主体相关的特权
echo "3. 搜索用户主体相关特权："
ipa privilege-find | grep -i -A1 -B1 "principal\|kerberos\|krb"
echo

# 4. 查看所有角色
echo "4. 查看所有角色："
ipa role-find | grep -E "(Role name|角色名称)"
echo

# 5. 查看User Administrator角色详情
echo "5. User Administrator角色详情："
ipa role-show "User Administrator" 2>/dev/null || echo "角色不存在或名称不同"
echo

# 6. 查看Help Desk角色详情
echo "6. Help Desk角色详情："
ipa role-show "Help Desk" 2>/dev/null || echo "角色不存在或名称不同"
echo

# 7. 查看IT Security Specialist角色详情
echo "7. IT Security Specialist角色详情："
ipa role-show "IT Security Specialist" 2>/dev/null || echo "角色不存在或名称不同"
echo

# 8. 查看wells用户当前权限
echo "8. wells用户当前权限："
ipa user-show wells --all | grep -E "(Member of|成员)"
echo

echo "=== 查找用户主体管理相关权限 ==="
echo "9. 搜索包含'Add'和'krbPrincipalName'的权限："
ipa privilege-find | grep -i -A2 -B2 "add.*krb\|krb.*add"
echo

echo "10. 搜索包含'Remove'和'krbPrincipalName'的权限："
ipa privilege-find | grep -i -A2 -B2 "remove.*krb\|krb.*remove"
echo

echo "11. 搜索包含'Modify'和'krbPrincipalName'的权限："
ipa privilege-find | grep -i -A2 -B2 "modify.*krb\|krb.*modify"
echo

echo "=== 建议操作 ==="
echo "基于FreeIPA 4.9.8，通常的做法是："
echo "1. 将用户添加到现有角色："
echo "   ipa role-add-member 'User Administrator' --users=wells"
echo "2. 或查看具体的权限名称后创建自定义角色"
