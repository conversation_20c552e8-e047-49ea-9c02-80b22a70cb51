[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automember_rebuild
Rebuild auto membership.

### Arguments
No arguments.

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* type : :ref:`StrEnum<StrEnum>`
 * Values: ('group', 'hostgroup')
* users : :ref:`Str<Str>`
* hosts : :ref:`Str<Str>`
* no_wait : :ref:`Flag<Flag>`
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences