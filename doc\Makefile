# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
PYTHON        ?= python3
VENVDIR       = ./.venv
SPHINXOPTS    ?= -W --keep-going -j 1
SPHINXBUILD   ?= PATH=$(VENVDIR)/bin:$$PATH sphinx-build
SOURCEDIR     = .
BUILDDIR      = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
	@echo "  lint        to check and lint documentation"
	@echo "  venv        to create an isolated venv"

.PHONY: help Makefile

# Catch-all target: route all unknown targets to <PERSON>phinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: lint
lint:
	$(MAKE) html SPHINXOPTS="$(SPHINXOPTS) -E -a"

.PHONY: distclean
distclean: clean
	rm -rf $(VENVDIR)

.PHONY: venv
venv:
	$(PYTHON) -m venv $(VENVDIR)
	$(VENVDIR)/bin/python3 -m pip install -U pip setuptools
	$(VENVDIR)/bin/python3 -m pip install -U -r ./requirements.txt \
		-c constraints.txt
