/* Authors: <AUTHORS>
 *
 * Copyright (C) 2010  Red Hat
 * see file 'COPYING' for use and warranty information
 *
 * This program is free software you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#include <locale.h>
#include <libintl.h>
#include <errno.h>

#include "config.h"

int init_gettext(void)
{
    char *c;

    c = setlocale(LC_ALL, "");
    if (!c) {
        return EIO;
    }

    errno = 0;
    c = bindtextdomain("ipa", LOCALEDIR);
    if (c == NULL) {
        return errno;
    }

    errno = 0;
    c = textdomain("ipa");
    if (c == NULL) {
        return errno;
    }

    return 0;
}
