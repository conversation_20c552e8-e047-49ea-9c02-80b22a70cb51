[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# user_remove_certmapdata
Remove one or more certificate mappings from the user entry.

### Arguments
|Name|Type|Required
|-|-|-
|uid|:ref:`Str<Str>`|True
|ipacertmapdata|:ref:`Str<Str>`|False

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* issuer : :ref:`DNParam<DNParam>`
* subject : :ref:`DNParam<DNParam>`
* certificate : :ref:`Certificate<Certificate>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences