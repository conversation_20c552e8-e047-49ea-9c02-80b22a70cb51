[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# user_del
Delete a user.

### Arguments
|Name|Type|Required
|-|-|-
|uid|:ref:`Str<Str>`|True

### Options
* continue : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* preserve : :ref:`Bool<Bool>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|ListOfPrimaryKeys

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences