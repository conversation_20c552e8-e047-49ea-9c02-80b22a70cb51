[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# passwd
Set a user's password.

### Arguments
|Name|Type|Required
|-|-|-
|principal|:ref:`Principal<Principal>`|True
|password|:ref:`Password<Password>`|True
|current_password|:ref:`Password<Password>`|True

### Options
* otp : :ref:`Password<Password>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences