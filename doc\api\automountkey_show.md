[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountkey_show
Display an automount key.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapautomountmapname|:ref:`IA5Str<IA5Str>`|True

### Options
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* automountkey : :ref:`IA5Str<IA5Str>` **(Required)**
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* automountinformation : :ref:`IA5Str<IA5Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences