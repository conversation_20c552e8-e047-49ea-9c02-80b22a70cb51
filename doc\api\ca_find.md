[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# ca_find
Search for CAs.

### Arguments
|Name|Type|Required
|-|-|-
|criteria|:ref:`Str<Str>`|False

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* cn : :ref:`Str<Str>`
* description : :ref:`Str<Str>`
* ipacaid : :ref:`Str<Str>`
* ipacasubjectdn : :ref:`DNParam<DNParam>`
* ipacaissuerdn : :ref:`DNParam<DNParam>`
* ipacarandomserialnumberversion : :ref:`Int<Int>`
* timelimit : :ref:`Int<Int>`
* sizelimit : :ref:`Int<Int>`
* version : :ref:`Str<Str>`
* pkey_only : :ref:`Flag<Flag>`
 * Default: False

### Output
|Name|Type
|-|-
|count|Output
|result|ListOfEntries
|summary|Output
|truncated|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences