/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#ifndef	_Int32_H_
#define	_Int32_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Int32 */
typedef long	 Int32_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Int32;
asn_struct_free_f Int32_free;
asn_struct_print_f Int32_print;
asn_constr_check_f Int32_constraint;
ber_type_decoder_f Int32_decode_ber;
der_type_encoder_f Int32_encode_der;
xer_type_decoder_f Int32_decode_xer;
xer_type_encoder_f Int32_encode_xer;

#ifdef __cplusplus
}
#endif

#endif	/* _Int32_H_ */
#include <asn_internal.h>
