#!/bin/bash

echo "=== 配置Kerberos主体管理权限 ==="
echo

# 确保以admin身份运行
echo "当前用户: $(klist | grep "Default principal" | cut -d: -f2 | tr -d ' ')"
echo

# 1. 检查目标权限是否存在
echo "1. 检查目标权限："
echo "检查 'System: Manage User Principals':"
ipa privilege-show "System: Manage User Principals" 2>/dev/null && echo "✓ 存在" || echo "✗ 不存在"

echo "检查 'System: Manage Service Principals':"
ipa privilege-show "System: Manage Service Principals" 2>/dev/null && echo "✓ 存在" || echo "✗ 不存在"

echo "检查 'System: Manage Host Principals':"
ipa privilege-show "System: Manage Host Principals" 2>/dev/null && echo "✓ 存在" || echo "✗ 不存在"
echo

# 2. 搜索实际的权限名称
echo "2. 搜索实际的主体管理权限："
ipa privilege-find | grep -i -A2 -B2 "principal.*manage\|manage.*principal"
echo

# 3. 搜索用户、服务、主机相关的权限
echo "3. 搜索相关权限："
echo "用户主体相关："
ipa privilege-find | grep -i -A1 -B1 "user.*principal\|principal.*user"
echo
echo "服务主体相关："
ipa privilege-find | grep -i -A1 -B1 "service.*principal\|principal.*service"
echo
echo "主机主体相关："
ipa privilege-find | grep -i -A1 -B1 "host.*principal\|principal.*host"
echo

# 4. 检查现有角色中是否包含这些权限
echo "4. 检查现有角色的权限："
echo "User Administrator角色权限："
ipa role-show "User Administrator" | grep -A10 "Privileges"
echo

echo "IT Security Specialist角色权限："
ipa role-show "IT Security Specialist" 2>/dev/null | grep -A10 "Privileges" || echo "角色不存在"
echo

# 5. 检查权限组位置
echo "5. 检查权限组位置："
ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=*principal*)" cn 2>/dev/null | grep "^cn:" | head -10
echo

echo "=== 建议操作 ==="
echo "基于检查结果，执行以下操作："
echo
echo "如果权限存在，创建自定义角色："
echo "ipa role-add 'Principal Manager' --desc='Manage Kerberos principals'"
echo "ipa role-add-privilege 'Principal Manager' --privileges='System: Manage User Principals'"
echo "ipa role-add-privilege 'Principal Manager' --privileges='System: Manage Service Principals'"  
echo "ipa role-add-privilege 'Principal Manager' --privileges='System: Manage Host Principals'"
echo "ipa role-add-member 'Principal Manager' --users=wells"
echo
echo "或者，如果权限名称不同，需要使用实际找到的权限名称"
