/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#include "GetKeytabControl.h"

static asn_TYPE_member_t asn_MBR_GetKeytabControl_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct GetKeytabControl, choice.newkeys),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_GKNewKeys,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		"newkeys"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GetKeytabControl, choice.curkeys),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_GKCurrentKeys,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		"curkeys"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GetKeytabControl, choice.reply),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_GKReply,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		"reply"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_GetKeytabControl_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* newkeys */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* curkeys */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* reply */
};
static asn_CHOICE_specifics_t asn_SPC_GetKeytabControl_specs_1 = {
	sizeof(struct GetKeytabControl),
	offsetof(struct GetKeytabControl, _asn_ctx),
	offsetof(struct GetKeytabControl, present),
	sizeof(((struct GetKeytabControl *)0)->present),
	asn_MAP_GetKeytabControl_tag2el_1,
	3,	/* Count of tags in the map */
	0,
	-1	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_GetKeytabControl = {
	"GetKeytabControl",
	"GetKeytabControl",
	CHOICE_free,
	CHOICE_print,
	CHOICE_constraint,
	CHOICE_decode_ber,
	CHOICE_encode_der,
	CHOICE_decode_xer,
	CHOICE_encode_xer,
	0, 0,	/* No PER support, use "-gen-PER" to enable */
	CHOICE_outmost_tag,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	0,	/* No PER visible constraints */
	asn_MBR_GetKeytabControl_1,
	3,	/* Elements count */
	&asn_SPC_GetKeytabControl_specs_1	/* Additional specs */
};

