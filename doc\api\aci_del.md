[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# aci_del
Delete ACI.

### Arguments
|Name|Type|Required
|-|-|-
|aciname|:ref:`Str<Str>`|True

### Options
* aciprefix : :ref:`StrEnum<StrEnum>` **(Required)**
 * Values: ('permission', 'delegation', 'selfservice', 'none')
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences