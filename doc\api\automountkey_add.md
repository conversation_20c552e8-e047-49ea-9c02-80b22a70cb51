[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountkey_add
Create a new automount key.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapautomountmapname|:ref:`IA5Str<IA5Str>`|True

### Options
* automountkey : :ref:`IA5Str<IA5Str>` **(Required)**
* automountinformation : :ref:`IA5Str<IA5Str>` **(Required)**
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* setattr : :ref:`Str<Str>`
* addattr : :ref:`Str<Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences