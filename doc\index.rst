.. FreeIPA documentation master file, created by
   sphinx-quickstart on Wed Mar 18 08:53:57 2020.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to FreeIPA's documentation!
===================================

What is FreeIPA?
----------------

FreeIPA is an integrated security information management solution combining
Linux (Fedora), 389 Directory Server, MIT Kerberos, DNS, Dogtag
(Certificate System). It consists of a web interface and command-line
administration tools.

FreeIPA is an integrated Identity and Authentication solution for
Linux/UNIX networked environments. A FreeIPA server provides centralized
authentication, authorization and account information by storing data about
user, groups, hosts and other objects necessary to manage the security
aspects of a network of computers.

FreeIPA is built on top of well known Open Source components and standard
protocols with a very strong focus on ease of management and automation of
installation and configuration tasks.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   designs/index.rst
   workshop.rst
   api/index.rst


Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
