<!DOCTYPE busconfig PUBLIC
 "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
 "http://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<busconfig>
 <!-- Only root can own (provide) the com.redhat.idm.trust service
       on the system bus. -->
  <policy user="root">
    <allow own="com.redhat.idm.trust"/>
    <allow send_destination="com.redhat.idm.trust"
           send_path="/"
           send_interface="com.redhat.idm.trust"
           send_member="fetch_domains"/>
  </policy>

  <!-- Allow anyone to call the introspection methods of the "/" object
       provided by the com.redhat.idm.trust service. -->
  <policy context="default">
    <allow send_destination="com.redhat.idm.trust"
           send_path="/"
           send_interface="org.freedesktop.DBus.Introspectable"
           send_member="Introspect"/>
    <allow send_destination="com.redhat.idm.trust"
           send_path="/"
           send_interface="org.freedesktop.DBus.Properties"
           send_member="GetAll"/>
    <allow send_destination="com.redhat.idm.trust"
           send_path="/"
           send_interface="org.freedesktop.DBus.Properties"
           send_member="Get"/>
  </policy>

  <policy user="ipaapi">
    <allow send_destination="com.redhat.idm.trust"
           send_path="/"
           send_interface="com.redhat.idm.trust"
           send_member="fetch_domains"/>
  </policy>

</busconfig>
