[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# trust_resolve
Resolve security identifiers of users and groups in trusted domains

### Arguments
No arguments.

### Options
* sids : :ref:`Str<Str>` **(Required)**
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|ListOfEntries

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences