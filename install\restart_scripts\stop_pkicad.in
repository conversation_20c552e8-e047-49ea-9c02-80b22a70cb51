#!/usr/bin/python3
#
# Authors: <AUTHORS>
#
# Copyright (C) 2012  Red Hat
# see file 'COPYING' for use and warranty information
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

import syslog
import traceback
from ipalib import api
from ipaplatform import services
from ipaplatform.paths import paths
from ipaserver.install import certs


def main():
    api.bootstrap(
        in_server=True, context='restart', confdir=paths.ETC_IPA, log=None
    )
    api.finalize()

    dogtag_service = services.knownservices['pki_tomcatd']

    certs.renewal_lock.acquire('renew_ca_cert')

    syslog.syslog(syslog.LOG_NOTICE, "Stopping %s" % dogtag_service.service_name)
    try:
        dogtag_service.stop('pki-tomcat')
    except Exception as e:
        syslog.syslog(
            syslog.LOG_ERR, "Cannot stop %s: %s" % (dogtag_service.service_name, e))
    else:
        syslog.syslog(
            syslog.LOG_NOTICE, "Stopped %s" % dogtag_service.service_name)

try:
    main()
except Exception:
    syslog.syslog(syslog.LOG_ERR, traceback.format_exc())
