[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# hbactest
Simulate use of Host-based access controls

### Arguments
No arguments.

### Options
* user : :ref:`Str<Str>` **(Required)**
* targethost : :ref:`Str<Str>` **(Required)**
* service : :ref:`Str<Str>` **(Required)**
* sourcehost : :ref:`Str<Str>`
* rules : :ref:`Str<Str>`
* nodetail : :ref:`Flag<Flag>`
 * Default: False
* enabled : :ref:`Flag<Flag>`
 * Default: False
* disabled : :ref:`Flag<Flag>`
 * Default: False
* sizelimit : :ref:`Int<Int>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|error|Output
|matched|Output
|notmatched|Output
|summary|Output
|value|Output
|warning|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences