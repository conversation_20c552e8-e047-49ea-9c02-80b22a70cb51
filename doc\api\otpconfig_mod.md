[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# otpconfig_mod
Modify OTP configuration options.

### Arguments
No arguments.

### Options
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* ipatokentotpauthwindow : :ref:`Int<Int>`
* ipatokentotpsyncwindow : :ref:`Int<Int>`
* ipatokenhotpauthwindow : :ref:`Int<Int>`
* ipatokenhotpsyncwindow : :ref:`Int<Int>`
* setattr : :ref:`Str<Str>`
* addattr : :ref:`Str<Str>`
* delattr : :ref:`Str<Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences