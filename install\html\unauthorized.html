<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Identity Management</title>
    <script type="text/javascript" src="../ui/js/libs/loader.js"></script>
    <script type="text/javascript">
        var dojoConfig = {
            baseUrl: "../ui/js",
            has: {
                'dojo-firebug': false,
                'dojo-debug-messages': true
            },
            parseOnLoad: false,
            async: true,
            packages: [
                {
                    name:'dojo',
                    location:'dojo'
                },
                {
                    name: 'freeipa',
                    location: 'freeipa'
                }
            ]
        };
        (function() {
            var icons = [
                '../ui/favicon.ico'
            ];
            var styles = [
                '../ui/css/patternfly.css',
                '../ui/css/ipa.css'
            ];
            var scripts = [
                '../ui/js/libs/jquery.js',
                '../ui/js/libs/jquery.ordered-map.js',
                '../ui/js/dojo/dojo.js'
            ];

            ipa_loader.scripts(scripts, function() {
                require([
                    'dojo/dom',
                    'freeipa/core',
                    'dojo/domReady!'
                    ],
                    function(dom) {
                        var text = require('freeipa/text');
                        var msg = text.get('@i18n:unauthorized-page');
                        if (msg) {
                            dom.byId('unauthorized-msg').innerHTML=msg;
                        }
                    });
            });
            ipa_loader.styles(styles);
            ipa_loader.icons(icons);
        })();
    </script>
</head>

<body class="info-page">

    <nav class="navbar navbar-default navbar-pf" role="navigation">
    <div class="navbar-header">
        <a class="brand" href="../ui/index.html"><img src="../ui/images/header-logo.png" alt="Identity Management"></a>
    </div>
    </nav>

    <div class="container-fluid">
    <div class="row">
    <div class="col-sm-12">
    <div id="unauthorized-msg">
    <noscript>

        <h1>Unable to verify your Kerberos credentials</h1>
        <p>
            Please make sure that you have valid Kerberos tickets (obtainable via <strong>kinit</strong>), and that you have configured your browser correctly.
        </p>

        <h2>Browser configuration</h2>

        <div id="first-time">
            <p>
                If this is your first time, please <a href="ssbrowser.html">configure your browser</a>.
            </p>
        </div>
    </noscript>
    </div>
    </div>
    </div>
    </div>

</body>

</html>
