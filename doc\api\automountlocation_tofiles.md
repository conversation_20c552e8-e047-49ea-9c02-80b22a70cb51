[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountlocation_tofiles
Generate automount files for a specific location.

### Arguments
|Name|Type|Required
|-|-|-
|cn|:ref:`Str<Str>`|True

### Options
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences