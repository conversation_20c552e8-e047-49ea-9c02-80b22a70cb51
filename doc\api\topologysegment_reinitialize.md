[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# topologysegment_reinitialize
Request a full re-initialization of the node retrieving data from the other node.

### Arguments
|Name|Type|Required
|-|-|-
|topologysuffixcn|:ref:`Str<Str>`|True
|cn|:ref:`Str<Str>`|True

### Options
* left : :ref:`Flag<Flag>`
 * Default: False
* right : :ref:`Flag<Flag>`
 * Default: False
* stop : :ref:`Flag<Flag>`
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences