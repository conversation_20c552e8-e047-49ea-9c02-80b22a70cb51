[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# trustconfig_show
Show global trust configuration.

### Arguments
No arguments.

### Options
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* trust_type : :ref:`StrEnum<StrEnum>` **(Required)**
 * Default: ad
 * Values: ('ad',)
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences