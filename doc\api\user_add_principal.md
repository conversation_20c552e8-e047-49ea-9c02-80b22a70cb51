[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# user_add_principal
Add new principal alias to the user entry

### Arguments
|Name|Type|Required
|-|-|-
|uid|:ref:`Str<Str>`|True
|krbprincipalname|:ref:`Principal<Principal>`|True

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences