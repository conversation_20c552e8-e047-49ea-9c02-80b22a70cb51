[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automember_del

Delete an automember rule.


### Arguments
|Name|Type|Required
|-|-|-
|cn|:ref:`Str<Str>`|True

### Options
* type : :ref:`StrEnum<StrEnum>` **(Required)**
 * Values: ('group', 'hostgroup')
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|ListOfPrimaryKeys

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences