/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#ifndef	_GK<PERSON>ew<PERSON>eys_H_
#define	_GKNewKeys_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include "Int32.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GKNewKeys */
typedef struct GKNewKeys {
	OCTET_STRING_t	 serviceIdentity;
	struct enctypes {
		A_SEQUENCE_OF(Int32_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} enctypes;
	OCTET_STRING_t	*password	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GKNewKeys_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GKNewKeys;

#ifdef __cplusplus
}
#endif

#endif	/* _GKNewKeys_H_ */
#include <asn_internal.h>
