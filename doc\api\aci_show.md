[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# aci_show
Display a single ACI given an ACI name.

### Arguments
|Name|Type|Required
|-|-|-
|aciname|:ref:`Str<Str>`|True

### Options
* aciprefix : :ref:`StrEnum<StrEnum>` **(Required)**
 * Values: ('permission', 'delegation', 'selfservice', 'none')
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* location : :ref:`DNParam<DNParam>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences