[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountkey_mod
Modify an automount key.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapautomountmapname|:ref:`IA5Str<IA5Str>`|True

### Options
* automountkey : :ref:`IA5Str<IA5Str>` **(Required)**
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* automountinformation : :ref:`IA5Str<IA5Str>`
* setattr : :ref:`Str<Str>`
* addattr : :ref:`Str<Str>`
* delattr : :ref:`Str<Str>`
* newautomountinformation : :ref:`IA5Str<IA5Str>`
* version : :ref:`Str<Str>`
* rename : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences