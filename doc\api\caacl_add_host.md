[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# caacl_add_host
Add target hosts and hostgroups to a CA ACL.

### Arguments
|Name|Type|Required
|-|-|-
|cn|:ref:`Str<Str>`|True

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`
* host : :ref:`Str<Str>`
* hostgroup : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|completed|Output
|failed|Output
|result|Entry

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences