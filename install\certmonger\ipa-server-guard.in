#!/usr/bin/python3
#
# Authors: <AUTHORS>
#
# Copyright (C) 2015  Red Hat
# see file 'COPYING' for use and warranty information
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

import os
# Prevent garbage from readline on standard output
# (see https://fedorahosted.org/freeipa/ticket/4064)
if not os.isatty(1):
    os.environ['TERM'] = 'dumb'
import sys

# Return codes. Names of the constants are taken from
# https://git.fedorahosted.org/cgit/certmonger.git/tree/src/submit-e.h
OPERATION_NOT_SUPPORTED_BY_HELPER = 6


def run_operation(cmd):
    from ipapython import ipautil

    result = ipautil.run(cmd, raiseonerr=False, env=os.environ)
    # Write bytes directly
    sys.stdout.buffer.write(result.raw_output)
    sys.stderr.buffer.write(result.raw_error_output)
    sys.stdout.flush()
    sys.stderr.flush()

    return result.returncode


def main():
    if len(sys.argv) < 2:
        raise RuntimeError("Not enough arguments")

    # Avoid the lock if the operation is unsupported by ipa-submit
    operation = os.environ.get('CERTMONGER_OPERATION')
    if operation not in ('IDENTIFY',
                         'FETCH-ROOTS',
                         'GET-NEW-REQUEST-REQUIREMENTS',
                         'SUBMIT',
                         'POLL'):
        return OPERATION_NOT_SUPPORTED_BY_HELPER

    if operation in ('SUBMIT', 'POLL', 'FETCH-ROOTS'):
        from ipaserver.install import certs
        with certs.renewal_lock:
            return run_operation(sys.argv[1:])
    else:
        return run_operation(sys.argv[1:])


try:
    sys.exit(main())
except Exception as e:
    import traceback
    import syslog
    syslog.syslog(syslog.LOG_ERR, traceback.format_exc())
    print("Internal error")
    sys.exit(3)
