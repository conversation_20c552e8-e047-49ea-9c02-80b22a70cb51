#!/bin/bash

echo "=== 管理用户主体权限设置 ==="
echo

# 1. 检查wells用户是否存在
echo "1. 检查wells用户："
if ipa user-show wells >/dev/null 2>&1; then
    echo "✓ wells用户存在"
    ipa user-show wells --all | grep -E "(User login|Full name|Email|Member of groups|Member of roles)"
else
    echo "✗ wells用户不存在，需要先创建用户"
    echo "创建用户命令："
    echo "ipa user-add wells --first=Wells --last=User --email=<EMAIL>"
    exit 1
fi
echo

# 2. 查看"System: Manage User Principals"权限详情
echo "2. 查看'System: Manage User Principals'权限："
ipa privilege-show "System: Manage User Principals" 2>/dev/null || echo "权限不存在或名称不正确"
echo

# 3. 搜索相关的用户主体管理权限
echo "3. 搜索用户主体相关权限："
ipa privilege-find --name="*principal*" --name="*user*" | grep -E "(Privilege name|Description)"
echo

# 4. 查看拥有用户主体管理权限的角色
echo "4. 查看相关角色："
ipa role-find | grep -A2 -B2 -i "user\|principal"
echo

# 5. 检查User Administrator角色
echo "5. 检查User Administrator角色："
ipa role-show "User Administrator" 2>/dev/null | grep -E "(Role name|Description|Member users|Privileges)"
echo

# 6. 检查wells用户当前的角色
echo "6. wells用户当前角色："
ipa user-show wells | grep "Member of roles"
echo

echo "=== 建议的操作 ==="
echo "基于上述信息，选择以下操作之一："
echo
echo "选项1 - 添加到User Administrator角色："
echo "ipa role-add-member 'User Administrator' --users=wells"
echo
echo "选项2 - 添加到Help Desk角色（如果只需要部分权限）："
echo "ipa role-add-member 'Help Desk' --users=wells"
echo
echo "选项3 - 创建自定义角色："
echo "ipa role-add 'Wells Principal Manager' --desc='Custom role for wells'"
echo "ipa role-add-privilege 'Wells Principal Manager' --privileges='System: Manage User Principals'"
echo "ipa role-add-member 'Wells Principal Manager' --users=wells"
echo

echo "=== 验证命令 ==="
echo "操作完成后，使用以下命令验证："
echo "ipa user-show wells | grep 'Member of roles'"
echo "kinit wells"
echo "ipa user-add-principal testuser --principal=<EMAIL>"
