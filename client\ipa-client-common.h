/* Authors: <AUTHORS>
 *
 * Copyright (C) 2010  Red Hat
 * see file 'COPYING' for use and warranty information
 *
 * This program is free software you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

#pragma once

#include <libintl.h>
#define _(STRING) gettext(STRING)

#include <stdint.h>
#ifndef discard_const
#define discard_const(ptr) ((void *)((uintptr_t)(ptr)))
#endif

int init_gettext(void);

typedef struct {
    char *payload;
    size_t size;
} curl_buffer;

typedef struct {
    char *dn;
    char *krb_principal;
    int is_provisioned;
} join_info;
