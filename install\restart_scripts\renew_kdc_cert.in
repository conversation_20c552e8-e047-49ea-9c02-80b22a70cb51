#!/usr/bin/python3
#
# Copyright (C) 2017  FreeIPA Contributors see COPYING for license
#

import syslog
import traceback

from ipaplatform import services
from ipaserver.install import certs


def main():
    with certs.renewal_lock:
        try:
            if services.knownservices.krb5kdc.is_running():
                syslog.syslog(syslog.LOG_NOTICE, 'restarting krb5kdc')
                services.knownservices.krb5kdc.restart()
        except Exception as e:
            syslog.syslog(
                syslog.LOG_ERR, "cannot restart krb5kdc: {}".format(e))


try:
    main()
except Exception:
    syslog.syslog(syslog.LOG_ERR, traceback.format_exc())
