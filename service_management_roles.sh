#!/bin/bash

echo "=== 服务管理权限分析 ==="
echo

# 1. 查看所有角色及其权限
echo "1. 查看所有角色："
ipa role-find | grep -E "(Role name|角色名称|Description|描述)"
echo

# 2. 查看IT Security Specialist角色（通常有服务管理权限）
echo "2. IT Security Specialist角色详情："
ipa role-show "IT Security Specialist" 2>/dev/null || echo "角色不存在"
echo

# 3. 查看Service Administrator角色
echo "3. Service Administrator角色详情："
ipa role-show "Service Administrator" 2>/dev/null || echo "角色不存在"
echo

# 4. 搜索服务相关的特权
echo "4. 搜索服务相关特权："
ipa privilege-find | grep -i -A1 -B1 "service\|kerberos"
echo

# 5. 查看Host Administrator角色
echo "5. Host Administrator角色详情："
ipa role-show "Host Administrator" 2>/dev/null || echo "角色不存在"
echo

# 6. 查看当前wells用户的完整权限
echo "6. wells用户当前完整信息："
ipa user-show wells --all | grep -E "(角色|role|权限|privilege|组|group)"
echo

echo "=== 建议操作 ==="
echo "基于分析结果，可以尝试以下操作："
echo
echo "选项1 - 添加到IT Security Specialist角色："
echo "ipa role-add-member 'IT Security Specialist' --users=wells"
echo
echo "选项2 - 添加到Service Administrator角色（如果存在）："
echo "ipa role-add-member 'Service Administrator' --users=wells"
echo
echo "选项3 - 添加到Host Administrator角色："
echo "ipa role-add-member 'Host Administrator' --users=wells"
echo
echo "选项4 - 创建自定义角色："
echo "ipa role-add 'Wells Service Manager'"
echo "# 然后添加相关权限"
echo
echo "选项5 - 直接测试用户主体管理："
echo "kinit wells"
echo "ipa user-add-principal admin --principal=<EMAIL>"
