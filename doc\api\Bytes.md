[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
.. _Bytes:

# Bytes
[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)

A parameter for binary data (stored in the ``str`` type).
This class is named *Bytes* instead of *Str* so it's aligned with the
Python v3 ``(str, unicode) => (bytes, str)`` clean-up.
See: http://docs.python.org/3.0/whatsnew/3.0.html
Also see the `Str` parameter.
