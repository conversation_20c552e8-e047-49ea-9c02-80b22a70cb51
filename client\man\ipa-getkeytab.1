.\" A man page for ipa-getkeytab
.\" Copyright (C) 2007 Red Hat, Inc.
.\"
.\" This program is free software; you can redistribute it and/or modify
.\" it under the terms of the GNU General Public License as published by
.\" the Free Software Foundation, either version 3 of the License, or
.\" (at your option) any later version.
.\"
.\" This program is distributed in the hope that it will be useful, but
.\" WITHOUT ANY WARRANTY; without even the implied warranty of
.\" MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
.\" General Public License for more details.
.\"
.\" You should have received a copy of the GNU General Public License
.\" along with this program.  If not, see <http://www.gnu.org/licenses/>.
.\"
.\" Author: <PERSON> <<EMAIL>>
.\" Author: Simo Sorce <<EMAIL>>
.\"
.TH "ipa-getkeytab" "1" "Oct 10 2007" "IPA" "IPA Manual Pages"
.SH "NAME"
ipa\-getkeytab \- Get a keytab for a Kerberos principal
.SH "SYNOPSIS"
ipa\-getkeytab \fB\-p\fR \fIprincipal\-name\fR \fB\-k\fR \fIkeytab\-file\fR [ \fB\-e\fR \fIencryption\-types\fR ] [ \fB\-s\fR \fIipaserver\fR ] [ \fB\-q\fR ] [ \fB\-D\fR|\fB\-\-binddn\fR \fIBINDDN\fR ] [ \fB\-w|\-\-bindpw\fR ] [ \fB-W\fR ] [ \fB\-P\fR|\fB\-\-password\fR \fIPASSWORD\fR ] [ \fB\-\-cacert \fICACERT\fR ] [ \fB\-H|\-\-ldapuri \fIURI\fR ] [ \fB\-Y|\-\-mech \fIGSSAPI|EXTERNAL\fR ] [ \fB\-r\fR ]

.SH "DESCRIPTION"
Retrieves a Kerberos \fIkeytab\fR.

Kerberos keytabs are used for services (like sshd) to
perform Kerberos authentication. A keytab is a file
with one or more secrets (or keys) for a Kerberos
principal.

A Kerberos service principal is a Kerberos identity
that can be used for authentication. Service principals
contain the name of the service, the hostname of the
server, and the realm name. For example, the following
is an example principal for an ldap server:

   ldap/<EMAIL>

When using ipa\-getkeytab the realm name is already
provided, so the principal name is just the service
name and hostname (ldap/foo.example.com from the
example above).

ipa-getkeytab is used during IPA client enrollment to retrieve a host service principal and store it in /etc/krb5.keytab. It is possible to retrieve the keytab without Kerberos credentials if the host was pre\-created with a one\-time password. The keytab can be retrieved by binding as the host and authenticating with this one\-time password. The \fB\-D|\-\-binddn\fR \fB\-w|\-\-bindpw\fR options are used for this authentication. \fB-W\fR can be used instead of \fB\-w|\-\-bindpw\fR to interactively prompt for the bind password.

\fBWARNING:\fR retrieving the keytab resets the secret for the Kerberos principal.
This renders all other keytabs for that principal invalid.
When multiple hosts or services need to share the same key (for instance in high availability or load balancing clusters), the \fB\-r\fR option must be used to retrieve the existing key instead of generating a new one (please refer to the EXAMPLES section).

Note that the user or host calling \fBipa-getkeytab\fR needs to be allowed to generate the key with \fBipa host\-allow\-create\-keytab\fR or \fBipa service\-allow\-create\-keytab\fR,
and the user or host calling \fBipa-getkeytab \-r\fR needs to be allowed to retrieve the keytab for the host or service with \fBipa host\-allow\-retrieve\-keytab\fR or \fBipa service\-allow\-retrieve\-keytab\fR.

.SH "OPTIONS"
.TP
\fB\-p principal\-name\fR
The non\-realm part of the full principal name.
.TP
\fB\-k keytab\-file\fR
The keytab file where to append the new key (will be
created if it does not exist).
.TP
\fB\-e encryption\-types\fR
The list of encryption types to use to generate keys.
ipa\-getkeytab will use local client defaults if not provided.
Valid values depend on the Kerberos library version and configuration.
Common values are:
aes256\-cts
aes128\-cts
aes256\-sha2
aes128\-sha2
camellia256\-cts\-cmac
camellia128\-cts\-cmac
arcfour\-hmac
.TP
\fB\-s ipaserver\fR
The IPA server to retrieve the keytab from (FQDN). If this option is not
provided the server name is read from the IPA configuration file
(/etc/ipa/default.conf). Cannot be used together with \fB\-H\fR. If the
value is _srv_ then DNS discovery will be used to determine a server.
If this discovery fails then it will fall back to using the configuration
file.
.TP
\fB\-q\fR
Quiet mode. Only errors are displayed.
.TP
\fB\-\-permitted\-enctypes\fR
This options returns a description of the permitted encryption types, like this:
Supported encryption types:
AES\-256 CTS mode with 96\-bit SHA\-1 HMAC
AES\-128 CTS mode with 96\-bit SHA\-1 HMAC
AES\-128 CTS mode with 128\-bit SHA\-256 HMAC
AES\-256 CTS mode with 192\-bit SHA\-384 HMAC
ArcFour with HMAC/md5
.TP
\fB\-P, \-\-password\fR
Use this password for the key instead of one randomly generated. The length of the password is limited by 1024 characters. Note that MIT Kerberos also limits passwords entered through kpasswd and kadmin commands to the same length.
.TP
\fB\-D, \-\-binddn\fR
The LDAP DN to bind as when retrieving a keytab without Kerberos credentials. Generally used with the \fB\-w\fR or \fB\-W\fR options.
.TP
\fB\-w, \-\-bindpw\fR
The LDAP password to use when not binding with Kerberos. \fB\-D\fR and \fB\-w\fR can not be used together with \fB\-Y\fR.
.TP
\fB\-W\fR
Interactive prompt for the bind password. \fB\-D\fR and \fB\-W\fR can not be used together with \fB\-Y\fR
.TP
\fB\-\-cacert\fR
The path to the IPA CA certificate used to validate LDAPS/STARTTLS connections.
Defaults to /etc/ipa/ca.crt
.TP
\fB\-H, \-\-ldapuri\fR
LDAP URI. If ldap:// is specified, STARTTLS is initiated by default. Can not be used with \fB\-s\fR.
.TP
\fB\-Y, \-\-mech\fR
SASL mechanism to use if \fB\-D\fR and \fB\-w\fR are not specified. Use either
GSSAPI or EXTERNAL.
.TP
\fB\-r\fR
Retrieve mode. Retrieve an existing key from the server instead of generating a
new one. This is incompatible with the \-\-password option, and will work only
against a IPA server more recent than version 3.3. The user requesting the
keytab must have access to the keys for this operation to succeed.
.SH "EXAMPLES"
Add and retrieve a keytab for the NFS service principal on
the host foo.example.com and save it in the file /tmp/nfs.keytab and retrieve just the aes256\-sha2 key.

.nf
   # ipa\-getkeytab \-p nfs/foo.example.com \-k /tmp/nfs.keytab \-e aes\-sha2
.fi

Add and retrieve a keytab for the ldap service principal on
the host foo.example.com and save it in the file /tmp/ldap.keytab.

.nf
   # ipa\-getkeytab \-s ipaserver.example.com \-p ldap/foo.example.com \-k /tmp/ldap.keytab
.fi

Retrieve a keytab using LDAP credentials (this will typically be done by \fBipa\-join(1)\fR when enrolling a client using the \fBipa\-client\-install(1)\fR command:

.nf
   # ipa\-getkeytab \-s ipaserver.example.com \-p host/foo.example.com \-k /etc/krb5.keytab \-D fqdn=foo.example.com,cn=computers,cn=accounts,dc=example,dc=com \-w password
.fi

Add and retrieve a keytab for a clustered HTTP service deployed on client1.example.com and client2.example.com (already enrolled), using the client-frontend.example.com host name:

.nf
   # ipa host-add client-frontend.example.com --ip-address ********
   # ipa service-add HTTP/client-frontend.example.com
   # ipa service-allow-retrieve-keytab HTTP/client-frontend.example.com --hosts={client1.example.com,client2.example.com}
   # ipa server-allow-create-keytab HTTP/client-frontend.example.com --hosts=client1.example.com
.fi

   On client1, generate and retrieve a new keytab for client-frontend.example.com:
.nf
   # kinit -k
   # ipa-getkeytab -p HTTP/client-frontend.example.com -k /tmp/http.keytab

.fi
   On client2, retrieve the existing keytab for client-frontend.example.com:
.nf
   # kinit -k
   # ipa-getkeytab -r -p HTTP/client-frontend.example.com -k /tmp/http.keytab
.fi

.SH "EXIT STATUS"
The exit status is 0 on success, nonzero on error.

0 Success

1 Kerberos context initialization failed

2 Incorrect usage

3 Out of memory

4 Invalid service principal name

5 No Kerberos credentials cache

6 No Kerberos principal and no bind DN and password

7 Failed to open keytab

8 Failed to create key material

9 Setting keytab failed

10 Bind password required when using a bind DN

11 Failed to add key to keytab

12 Failed to close keytab
