[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# otptoken_mod
Modify a OTP token.

### Arguments
|Name|Type|Required
|-|-|-
|ipatokenuniqueid|:ref:`Str<Str>`|True

### Options
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* no_members : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* description : :ref:`Str<Str>`
* ipatokenowner : :ref:`Str<Str>`
* ipatokendisabled : :ref:`Bool<Bool>`
* ipatokennotbefore : :ref:`DateTime<DateTime>`
* ipatokennotafter : :ref:`DateTime<DateTime>`
* ipatokenvendor : :ref:`Str<Str>`
* ipatokenmodel : :ref:`Str<Str>`
* ipatokenserial : :ref:`Str<Str>`
* setattr : :ref:`Str<Str>`
* addattr : :ref:`Str<Str>`
* delattr : :ref:`Str<Str>`
* version : :ref:`Str<Str>`
* rename : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences