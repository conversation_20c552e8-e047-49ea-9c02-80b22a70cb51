[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# location_show
Display information about an IPA location.

### Arguments
|Name|Type|Required
|-|-|-
|idnsname|:ref:`DNSNameParam<DNSNameParam>`|True

### Options
* rights : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|servers|Output
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences