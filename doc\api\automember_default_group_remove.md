[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automember_default_group_remove

Remove default (fallback) group for all unmatched entries.


### Arguments
No arguments.

### Options
* type : :ref:`StrEnum<StrEnum>` **(Required)**
 * Values: ('group', 'hostgroup')
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences