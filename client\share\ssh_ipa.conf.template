# IPA-related configuration changes to ssh_config
#
PubkeyAuthentication yes
${ENABLEPROXY}GlobalKnownHostsFile $KNOWNHOSTS
${VERIFYHOSTKEYDNS}VerifyHostKeyDNS yes

# use sss_ssh_knownhosts if available
# assumes that if a user does not have shell (/sbin/nologin),
# this will return nonzero exit code and proxy command will be ignored
${ENABLEKNOWNHOSTS}Match exec true
${ENABLEKNOWNHOSTS}	KnownHostsCommand $KNOWNHOSTSCOMMAND %H

# assumes that if a user does not have shell (/sbin/nologin),
# this will return nonzero exit code and proxy command will be ignored
${ENABLEPROXY}Match exec true
${ENABLEPROXY}	ProxyCommand $KNOWNHOSTSPROXY -p %p %h
