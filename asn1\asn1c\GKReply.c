/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#include "GKReply.h"

static asn_TYPE_member_t asn_MBR_keys_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_Krb<PERSON>ey,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		""
		},
};
static const ber_tlv_tag_t asn_DEF_keys_tags_3[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_keys_specs_3 = {
	sizeof(struct keys),
	offsetof(struct keys, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_keys_3 = {
	"keys",
	"keys",
	SEQUENCE_OF_free,
	SEQUENCE_OF_print,
	SEQUENCE_OF_constraint,
	SEQUENCE_OF_decode_ber,
	SEQUENCE_OF_encode_der,
	SEQUENCE_OF_decode_xer,
	SEQUENCE_OF_encode_xer,
	0, 0,	/* No PER support, use "-gen-PER" to enable */
	0,	/* Use generic outmost tag fetcher */
	asn_DEF_keys_tags_3,
	sizeof(asn_DEF_keys_tags_3)
		/sizeof(asn_DEF_keys_tags_3[0]), /* 1 */
	asn_DEF_keys_tags_3,	/* Same as above */
	sizeof(asn_DEF_keys_tags_3)
		/sizeof(asn_DEF_keys_tags_3[0]), /* 1 */
	0,	/* No PER visible constraints */
	asn_MBR_keys_3,
	1,	/* Single element */
	&asn_SPC_keys_specs_3	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_GKReply_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct GKReply, newkvno),
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_Int32,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		"newkvno"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct GKReply, keys),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_keys_3,
		0,	/* Defer constraints checking to the member type */
		0,	/* PER is not compiled, use -gen-PER */
		0,
		"keys"
		},
};
static const ber_tlv_tag_t asn_DEF_GKReply_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_GKReply_tag2el_1[] = {
    { (ASN_TAG_CLASS_UNIVERSAL | (2 << 2)), 0, 0, 0 }, /* newkvno */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 1, 0, 0 } /* keys */
};
static asn_SEQUENCE_specifics_t asn_SPC_GKReply_specs_1 = {
	sizeof(struct GKReply),
	offsetof(struct GKReply, _asn_ctx),
	asn_MAP_GKReply_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* Start extensions */
	-1	/* Stop extensions */
};
asn_TYPE_descriptor_t asn_DEF_GKReply = {
	"GKReply",
	"GKReply",
	SEQUENCE_free,
	SEQUENCE_print,
	SEQUENCE_constraint,
	SEQUENCE_decode_ber,
	SEQUENCE_encode_der,
	SEQUENCE_decode_xer,
	SEQUENCE_encode_xer,
	0, 0,	/* No PER support, use "-gen-PER" to enable */
	0,	/* Use generic outmost tag fetcher */
	asn_DEF_GKReply_tags_1,
	sizeof(asn_DEF_GKReply_tags_1)
		/sizeof(asn_DEF_GKReply_tags_1[0]), /* 1 */
	asn_DEF_GKReply_tags_1,	/* Same as above */
	sizeof(asn_DEF_GKReply_tags_1)
		/sizeof(asn_DEF_GKReply_tags_1[0]), /* 1 */
	0,	/* No PER visible constraints */
	asn_MBR_GKReply_1,
	2,	/* Elements count */
	&asn_SPC_GKReply_specs_1	/* Additional specs */
};

