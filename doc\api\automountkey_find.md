[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountkey_find
Search for an automount key.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapautomountmapname|:ref:`IA5Str<IA5Str>`|True
|criteria|:ref:`Str<Str>`|False

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* automountkey : :ref:`IA5Str<IA5Str>`
* automountinformation : :ref:`IA5Str<IA5Str>`
* timelimit : :ref:`Int<Int>`
* sizelimit : :ref:`Int<Int>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|count|Output
|result|ListOfEntries
|summary|Output
|truncated|Output

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences