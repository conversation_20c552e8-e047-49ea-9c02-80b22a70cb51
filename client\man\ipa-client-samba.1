.\" A man page for ipa-client-samba
.\" Copyright (C) 2008-2016  FreeIPA Contributors see COPYING for license
.\"
.TH "ipa-client-samba" "1" "Jun 10 2019" "IPA" "IPA Manual Pages"
.SH "NAME"
ipa\-client\-samba \- Configure Samba file server on an IPA client
.SH "SYNOPSIS"
ipa\-client\-samba [\fIOPTION\fR]...
.SH "DESCRIPTION"
Configures a Samba file server on the client machine to use IPA domain controller for authentication and identity services.

The tool configures Samba file server to be a domain member of IPA domain. Samba file server will use SSSD to resolve information about users and groups, and will use IPA master it is enrolled against as its domain controller.

It is not possible to reconciliate original Samba environment if that was pre-existing on the client with new configuration. Samba databases will be updated to follow IPA domain details and \fBsmb.conf\fR configuration will will be overwritten. It is recommended to enable Samba suite on a freshly deployed IPA client.

.TP
During the configuration process, the tool will perform following steps:

1. Discover details of IPA domain: realm, domain SID, domain ID range

2. Discover details of trusted Active Directory domains: domain name, domain SID, domain ID range

3. Create Samba configuration file using the details discovered above.

4. Create Samba Kerberos service using host credentials and fetch its keytab into /etc/samba/samba.keytab. The Kerberos service key is pre-set to a randomly generated value that is shared with Samba.

5. Populate Samba databases by setting the domain details and the randomly generated machine account password from the previous step.

6. Create a default [homes] share to allow users to log in to their home directories unless \-\-no\-homes option was specified.

.TP
The tool does not start nor does it enable Samba file services after the configuration. In order to enable and start Samba file services, one needs to enable both \fBsmb.service\fR and \fBwinbind.service\fR system services. Please check that \fB/etc/samba/smb.conf\fR contains all settings for your use case as starting Samba service will make identity mapping details written into the Samba databases. To enable and start Samba file services at the same time one can use \fBsystemctl enable \-\-now\fR command:

systemctl enable --now smb winbind

.SS "Assumptions"
The ipa\-client\-samba script assumes that the machine has already been enrolled into IPA.

.SS "IPA Master Requirements"
At least one IPA master must hold a \fBTrust Controller\fR role. This can be achieved by running ipa\-adtrust\-install on the IPA master. The utility will configure IPA master to be a domain controller for IPA domain.

IPA master holding a \fBTrust Controller\fR role has also to have support for a special service command to create SMB service, \fBipa service-add-smb\fR. This command is available with IPA 4.8.0 or later release.

.SH "OPTIONS"
.SS "BASIC OPTIONS"
.TP
\fB\-\-server\fR=\fISERVER\fR
Set the FQDN of the IPA server to connect to. Under normal circumstances, this option is not needed as the server to use is discovered automatically.
.TP
\fB\-\-no\-homes\fR
Do not configure a \fB[homes]\fR share by default to allow users to access their home directories.
.TP
\fB\-\-no\-nfs\fR
Do not enable SELinux booleans to allow Samba to re-share NFS shares.
.TP
\fB\-\-netbios-name\fR=\fINETBIOS_NAME\fR
NetBIOS name of this machine. If not provided then this is determined based on the leading component of the hostname.
.TP
\fB\-d\fR, \fB\-\-debug\fR
Print debugging information to stdout
.TP
\fB\-U\fR, \fB\-\-unattended\fR
Unattended installation. The user will not be prompted.
.TP
\fB\-\-uninstall\fR
Revert Samba suite configuration changes and remove SMB service principal. It is not possible to preserve original Samba configuration: while \fBsmb.conf\fR configuration file will be restored, various Samba databases would not be restored. In general, it is not possible to restore full original Samba environment.
.TP
\fB\-\-force\fR
Force through the installation steps even if they were done before

.SH "FILES"
.TP
Files that will be replaced if Samba is configured:

/etc/samba/smb.conf
.br
/etc/samba/samba.keytab

.SH "EXIT STATUS"
0 if the installation was successful

1 if an error occurred

.SH "SEE ALSO"
.BR smb.conf(5),
.BR krb5.conf(5),
.BR sssd.conf(5),
.BR systemctl(1)
