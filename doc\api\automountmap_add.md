[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountmap_add
Create a new automount map.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapname|:ref:`IA5Str<IA5Str>`|True

### Options
* all : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* raw : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* description : :ref:`Str<Str>`
* setattr : :ref:`Str<Str>`
* addattr : :ref:`Str<Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Entry
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences