#!/usr/bin/python3
#
# Authors: <AUTHORS>
#   <PERSON> <<EMAIL>>
#
# Copyright (C) 2013  Red Hat
# see file 'COPYING' for use and warranty information
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

import sys
import os
import syslog
import tempfile
import shutil
import traceback

from ipalib.install.kinit import kinit_keytab
from ipalib import api, x509
from ipaserver.install import certs, cainstance
from ipaplatform.paths import paths


def _main():
    api.bootstrap(
        in_server=True, context='restart', confdir=paths.ETC_IPA, log=None
    )
    api.finalize()

    tmpdir = tempfile.mkdtemp(prefix="tmp-")
    try:
        principal = str('host/%s@%s' % (api.env.host, api.env.realm))
        ccache_filename = os.path.join(tmpdir, 'ccache')
        kinit_keytab(principal, paths.KRB5_KEYTAB, ccache_filename)
        os.environ['KRB5CCNAME'] = ccache_filename

        api.Backend.ldap2.connect()

        ca = cainstance.CAInstance(host_name=api.env.host)
        ra_certpath = paths.RA_AGENT_PEM
        if ca.is_renewal_master():
            # Fetch the new certificate
            try:
                cert = x509.load_certificate_from_file(ra_certpath)
            except IOError as e:
                syslog.syslog(
                    syslog.LOG_ERR, "Can't open '{certpath}': {err}"
                    .format(certpath=ra_certpath, err=e)
                )
                sys.exit(1)
            except (TypeError, ValueError):
                syslog.syslog(
                    syslog.LOG_ERR, "'{certpath}' is not a valid certificate "
                    "file".format(certpath=ra_certpath)
                )
                sys.exit(1)

            # Load it into dogtag
            cainstance.update_people_entry(cert)
    finally:
        if api.Backend.ldap2.isconnected():
            api.Backend.ldap2.disconnect()
        shutil.rmtree(tmpdir)


def main():
    try:
        _main()
    finally:
        # lock acquired in renew_ra_cert_pre
        certs.renewal_lock.release('renew_ra_cert')


try:
    main()
except Exception:
    syslog.syslog(syslog.LOG_ERR, traceback.format_exc())
