#!/bin/bash

echo "=== 将wells用户添加到权限组 ==="
echo

# 1. 获取wells用户的完整DN
echo "1. 获取wells用户的DN："
WELLS_DN=$(ipa user-show wells --raw | grep "^  dn:" | cut -d: -f2- | sed 's/^ *//')
echo "Wells用户DN: $WELLS_DN"
echo

# 2. 检查目标权限组是否存在
echo "2. 检查权限组："
echo "检查 System: Manage User Principals 权限组："
ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=System: Manage User Principals)" dn 2>/dev/null | grep "^dn:"

echo "检查 System: Manage Service Principals 权限组："
ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=System: Manage Service Principals)" dn 2>/dev/null | grep "^dn:"

echo "检查 System: Manage Host Principals 权限组："
ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=System: Manage Host Principals)" dn 2>/dev/null | grep "^dn:"
echo

# 3. 搜索所有包含principal的权限组
echo "3. 搜索所有包含principal的权限组："
ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=*principal*)" cn 2>/dev/null | grep "^cn:" | head -10
echo

# 4. 查看权限组的结构
echo "4. 查看权限组结构示例："
FIRST_PERMISSION=$(ldapsearch -Y GSSAPI -b "cn=permissions,cn=pbac,dc=rois,dc=local" "(cn=*principal*)" dn 2>/dev/null | grep "^dn:" | head -1 | cut -d: -f2- | sed 's/^ *//')
if [ ! -z "$FIRST_PERMISSION" ]; then
    echo "示例权限组: $FIRST_PERMISSION"
    ldapsearch -Y GSSAPI -b "$FIRST_PERMISSION" '*' 2>/dev/null | head -20
fi
echo

echo "=== 准备LDIF文件 ==="
echo "5. 创建LDIF文件来添加wells用户到权限组："

# 创建LDIF文件
cat > add_wells_permissions.ldif << EOF
# 添加wells到System: Manage User Principals权限组
dn: cn=System: Manage User Principals,cn=permissions,cn=pbac,dc=rois,dc=local
changetype: modify
add: member
member: $WELLS_DN

# 添加wells到System: Manage Service Principals权限组
dn: cn=System: Manage Service Principals,cn=permissions,cn=pbac,dc=rois,dc=local
changetype: modify
add: member
member: $WELLS_DN

# 添加wells到System: Manage Host Principals权限组
dn: cn=System: Manage Host Principals,cn=permissions,cn=pbac,dc=rois,dc=local
changetype: modify
add: member
member: $WELLS_DN
EOF

echo "LDIF文件内容："
cat add_wells_permissions.ldif
echo

echo "=== 执行操作 ==="
echo "6. 应用LDIF文件："
if ldapmodify -Y GSSAPI -f add_wells_permissions.ldif; then
    echo "✓ 成功添加wells用户到权限组"
else
    echo "✗ 添加失败，可能的原因："
    echo "  - 权限组名称不正确"
    echo "  - 权限组不存在"
    echo "  - 权限不足"
    echo "  - LDIF格式错误"
fi
echo

echo "=== 验证结果 ==="
echo "7. 验证wells用户是否被添加到权限组："
echo "检查System: Manage User Principals权限组成员："
ldapsearch -Y GSSAPI -b "cn=System: Manage User Principals,cn=permissions,cn=pbac,dc=rois,dc=local" member 2>/dev/null | grep "member:"

echo "检查System: Manage Service Principals权限组成员："
ldapsearch -Y GSSAPI -b "cn=System: Manage Service Principals,cn=permissions,cn=pbac,dc=rois,dc=local" member 2>/dev/null | grep "member:"

echo "检查System: Manage Host Principals权限组成员："
ldapsearch -Y GSSAPI -b "cn=System: Manage Host Principals,cn=permissions,cn=pbac,dc=rois,dc=local" member 2>/dev/null | grep "member:"
echo

echo "=== 测试权限 ==="
echo "8. 测试wells用户权限："
echo "切换到wells用户并测试："
echo "kdestroy && kinit wells"
echo "# 测试修改用户krbCanonicalName"
echo "ipa user-add testuser2 --first=Test --last=User2"
echo "# 然后尝试修改其krbCanonicalName"
