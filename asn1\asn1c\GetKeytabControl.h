/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "KeytabModule"
 * 	found in "ipa.asn1"
 * 	`asn1c -fskeletons-copy -fnative-types`
 */

#ifndef	_GetKeytabControl_H_
#define	_GetKeytabControl_H_


#include <asn_application.h>

/* Including external dependencies */
#include "GKNewKeys.h"
#include "GKCurrentKeys.h"
#include "GKReply.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum GetKeytabControl_PR {
	GetKeytabControl_PR_NOTHING,	/* No components present */
	GetKeytabControl_PR_newkeys,
	GetKeytabControl_PR_curkeys,
	GetKeytabControl_PR_reply
} GetKeytabControl_PR;

/* GetKeytabControl */
typedef struct GetKeytabControl {
	GetKeytabControl_PR present;
	union GetKeytabControl_u {
		GKNewKeys_t	 newkeys;
		GKCurrentKeys_t	 curkeys;
		GKReply_t	 reply;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GetKeytabControl_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GetKeytabControl;

#ifdef __cplusplus
}
#endif

#endif	/* _GetKeytabControl_H_ */
#include <asn_internal.h>
