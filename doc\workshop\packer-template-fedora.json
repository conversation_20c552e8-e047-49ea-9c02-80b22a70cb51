{"builders": [{"type": "qemu", "iso_url": "{{user `iso_url`}}", "iso_checksum": "{{user `iso_checksum`}}", "output_directory": "output-{{user `box_name`}}-x86_64-{{build_type}}", "vm_name": "packer-{{user `box_name`}}-x86_64", "disk_size": "{{user `disk_size`}}", "headless": "{{user `headless`}}", "http_directory": ".", "boot_wait": "5s", "boot_command": ["<tab> ", "inst.ks=http://{{.HTTPIP}}:{{.HTTPPort}}/anaconda-ks.cfg ", "biosdevname=0 ", "net.ifnames=0 ", "<enter>"], "ssh_timeout": "{{user `ssh_timeout`}}", "ssh_username": "vagrant", "ssh_password": "vagrant", "shutdown_command": "sudo systemctl poweroff", "qemuargs": [["-m", "{{user `memory`}}"], ["-smp", "{{user `cpus`}}"]]}, {"type": "virtualbox-iso", "guest_os_type": "Fedora_64", "iso_url": "{{user `iso_url`}}", "iso_checksum": "{{user `iso_checksum`}}", "output_directory": "output-{{user `box_name`}}-x86_64-{{build_type}}", "vm_name": "packer-{{user `box_name`}}-x86_64", "disk_size": "{{user `disk_size`}}", "headless": "{{user `headless`}}", "http_directory": ".", "boot_wait": "5s", "boot_command": ["<tab> ", "inst.ks=http://{{.HTTPIP}}:{{.HTTPPort}}/anaconda-ks.cfg ", "biosdevname=0 ", "net.ifnames=0 ", "<enter>"], "ssh_timeout": "{{user `ssh_timeout`}}", "ssh_username": "vagrant", "ssh_password": "vagrant", "shutdown_command": "sudo systemctl poweroff", "vboxmanage": [["modifyvm", "{{.Name}}", "--memory", "{{user `memory`}}"], ["modifyvm", "{{.Name}}", "--cpus", "{{user `cpus`}}"]]}], "provisioners": [{"type": "shell", "scripts": ["workshop-install-packages.sh"]}], "post-processors": [{"type": "vagrant", "compression_level": "{{user `compression_level`}}", "output": "{{user `box_name`}}-x86_64-{{.Provider}}.box"}], "variables": {"box_name": "fedora-34", "iso_url": "{{user `mirror`}}/releases/34/Server/x86_64/iso/Fedora-Server-netinst-x86_64-34-1.2.iso", "iso_checksum": "file:{{user `mirror`}}/releases/34/Server/x86_64/iso/Fedora-Server-34-1.2-x86_64-CHECKSUM", "compression_level": "9", "cpus": "1", "disk_size": "40000", "headless": "false", "memory": "1024", "mirror": "http://download.fedoraproject.org/pub/fedora/linux", "ssh_timeout": "60m"}}