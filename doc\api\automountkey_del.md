[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# automountkey_del
Delete an automount key.

### Arguments
|Name|Type|Required
|-|-|-
|automountlocationcn|:ref:`Str<Str>`|True
|automountmapautomountmapname|:ref:`IA5Str<IA5Str>`|True

### Options
* continue : :ref:`Flag<Flag>` **(Required)**
 * Default: False
* automountkey : :ref:`IA5Str<IA5Str>` **(Required)**
* automountinformation : :ref:`IA5Str<IA5Str>`
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|ListOfPrimaryKeys

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences