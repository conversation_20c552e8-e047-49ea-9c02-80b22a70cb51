[//]: # (THE CONTENT BELOW IS GENERATED. DO NOT EDIT.)
# user_unlock

Unlock a user account

An account may become locked if the password is entered incorrectly too
many times within a specific time period as controlled by password
policy. A locked account is a temporary condition and may be unlocked by
an administrator.

### Arguments
|Name|Type|Required
|-|-|-
|uid|:ref:`Str<Str>`|True

### Options
* version : :ref:`Str<Str>`

### Output
|Name|Type
|-|-
|result|Output
|summary|Output
|value|PrimaryKey

[//]: # (ADD YOUR NOTES BELOW. THESE WILL BE PICKED EVERY TIME THE DOCS ARE REGENERATED. //end)
### Semantics

### Notes

### Version differences